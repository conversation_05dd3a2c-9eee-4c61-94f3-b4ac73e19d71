<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Télécharger CapsulCall Logo PNG</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            text-align: center;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(30, 64, 175, 0.1);
        }
        
        h1 {
            color: #1e40af;
            margin-bottom: 30px;
        }
        
        .logo-display {
            margin: 30px 0;
            padding: 30px;
            background: #f8fafc;
            border-radius: 15px;
            border: 2px dashed #cbd5e1;
        }
        
        #logoCanvas {
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: white;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        .instructions {
            background: #eff6ff;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: left;
        }
        
        .instructions h3 {
            color: #1e40af;
            margin-top: 0;
        }
        
        .quality-options {
            margin: 20px 0;
        }
        
        .quality-btn {
            background: #f1f5f9;
            border: 2px solid #cbd5e1;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quality-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Télécharger CapsulCall Logo PNG</h1>
        
        <div class="logo-display">
            <canvas id="logoCanvas" width="1000" height="300"></canvas>
        </div>
        
        <div class="quality-options">
            <h3>Choisir la qualité :</h3>
            <button class="quality-btn active" onclick="setQuality(1000, 300)">Standard (1000x300)</button>
            <button class="quality-btn" onclick="setQuality(2000, 600)">Haute Qualité (2000x600)</button>
            <button class="quality-btn" onclick="setQuality(3000, 900)">Très Haute Qualité (3000x900)</button>
        </div>
        
        <button class="download-btn" onclick="downloadPNG()">
            📥 Télécharger en PNG
        </button>
        
        <button class="download-btn" onclick="downloadJPG()">
            📥 Télécharger en JPG
        </button>
        
        <div class="instructions">
            <h3>Instructions :</h3>
            <ol>
                <li>Choisissez la qualité désirée (plus haute = meilleure qualité mais fichier plus lourd)</li>
                <li>Cliquez sur "Télécharger en PNG" pour un fond transparent</li>
                <li>Cliquez sur "Télécharger en JPG" pour un fond blanc</li>
                <li>Le fichier sera automatiquement téléchargé dans votre dossier Téléchargements</li>
            </ol>
        </div>
    </div>

    <script>
        let currentWidth = 1000;
        let currentHeight = 300;
        
        function setQuality(width, height) {
            currentWidth = width;
            currentHeight = height;
            
            // Mettre à jour les boutons
            document.querySelectorAll('.quality-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Redessiner le logo
            drawLogo();
        }
        
        function drawLogo() {
            const canvas = document.getElementById('logoCanvas');
            const ctx = canvas.getContext('2d');
            
            // Ajuster la taille d'affichage du canvas
            canvas.width = Math.min(currentWidth, 800);
            canvas.height = Math.min(currentHeight, 240);
            
            // Effacer le canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Facteur d'échelle pour l'affichage
            const scale = canvas.width / 1000;
            ctx.scale(scale, scale);
            
            // Dessiner le logo
            drawCapsulCallLogo(ctx);
        }
        
        function drawCapsulCallLogo(ctx) {
            // Fond blanc
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 1000, 300);
            
            // Gradient bleu principal
            const primaryGradient = ctx.createLinearGradient(0, 0, 1000, 0);
            primaryGradient.addColorStop(0, '#1e40af');
            primaryGradient.addColorStop(1, '#3b82f6');
            
            // Gradient bleu accent
            const accentGradient = ctx.createLinearGradient(0, 0, 1000, 0);
            accentGradient.addColorStop(0, '#60a5fa');
            accentGradient.addColorStop(1, '#2563eb');
            
            // Dessiner la capsule principale
            ctx.fillStyle = primaryGradient;
            ctx.beginPath();
            ctx.roundRect(60, 50, 120, 160, 60);
            ctx.fill();
            
            // Ligne de séparation dans la capsule
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fillRect(70, 125, 100, 4);
            
            // Point de communication
            ctx.fillStyle = accentGradient;
            ctx.beginPath();
            ctx.arc(120, 100, 12, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.arc(120, 100, 6, 0, 2 * Math.PI);
            ctx.fill();
            
            // Ondes de signal
            ctx.strokeStyle = accentGradient;
            ctx.lineWidth = 4;
            ctx.globalAlpha = 0.7;
            
            // Première onde
            ctx.beginPath();
            ctx.arc(120, 130, 20, -0.5, 0.5);
            ctx.stroke();
            
            // Deuxième onde
            ctx.globalAlpha = 0.5;
            ctx.beginPath();
            ctx.arc(120, 130, 35, -0.5, 0.5);
            ctx.stroke();
            
            // Troisième onde
            ctx.globalAlpha = 0.3;
            ctx.beginPath();
            ctx.arc(120, 130, 50, -0.5, 0.5);
            ctx.stroke();
            
            ctx.globalAlpha = 1;
            
            // Texte CapsulCall
            ctx.font = 'bold 72px Arial, sans-serif';
            ctx.fillStyle = primaryGradient;
            ctx.fillText('CapsulCall', 250, 140);
            
            // Ligne de séparation
            ctx.fillStyle = accentGradient;
            ctx.globalAlpha = 0.3;
            ctx.fillRect(250, 170, 560, 2);
            ctx.globalAlpha = 1;
            
            // Sous-titre
            ctx.font = '22px Arial, sans-serif';
            ctx.fillStyle = '#64748b';
            ctx.fillText('Centre d\'Appel • Assurances Suisses • Télé-Secrétariat Médical', 250, 210);
        }
        
        function downloadPNG() {
            const canvas = document.createElement('canvas');
            canvas.width = currentWidth;
            canvas.height = currentHeight;
            const ctx = canvas.getContext('2d');
            
            // Dessiner le logo à la taille finale
            const scale = currentWidth / 1000;
            ctx.scale(scale, scale);
            drawCapsulCallLogo(ctx);
            
            // Télécharger
            const link = document.createElement('a');
            link.download = `capsulcall-logo-modern-${currentWidth}x${currentHeight}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadJPG() {
            const canvas = document.createElement('canvas');
            canvas.width = currentWidth;
            canvas.height = currentHeight;
            const ctx = canvas.getContext('2d');
            
            // Fond blanc pour JPG
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, currentWidth, currentHeight);
            
            // Dessiner le logo à la taille finale
            const scale = currentWidth / 1000;
            ctx.scale(scale, scale);
            drawCapsulCallLogo(ctx);
            
            // Télécharger
            const link = document.createElement('a');
            link.download = `capsulcall-logo-modern-${currentWidth}x${currentHeight}.jpg`;
            link.href = canvas.toDataURL('image/jpeg', 0.95);
            link.click();
        }
        
        // Dessiner le logo au chargement
        window.onload = function() {
            drawLogo();
        };
    </script>
</body>
</html>
