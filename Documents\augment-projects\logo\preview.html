<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CapsulCall - Aperçu des Logos</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(30, 64, 175, 0.1);
        }
        
        h1 {
            color: #1e40af;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .logo-section {
            margin-bottom: 60px;
            padding: 30px;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: #f8fafc;
        }
        
        .logo-section h2 {
            color: #3b82f6;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 30px;
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .description {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            text-align: center;
        }
        
        .features {
            background: #eff6ff;
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .features h3 {
            color: #1e40af;
            margin-bottom: 20px;
        }
        
        .features ul {
            color: #475569;
            line-height: 1.8;
        }
        
        .features li {
            margin-bottom: 8px;
        }
        
        .download-section {
            text-align: center;
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 15px;
            color: white;
        }
        
        .download-section h3 {
            margin-bottom: 20px;
        }
        
        .file-list {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .file-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 25px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CapsulCall - Logos Créés</h1>
        
        <div class="logo-section">
            <h2>Version 1 - Logo Classique avec Capsule</h2>
            <div class="logo-container">
                <object data="capsulcall-logo.svg" type="image/svg+xml" width="400" height="200">
                    Votre navigateur ne supporte pas les SVG
                </object>
            </div>
            <div class="description">
                Design avec une capsule stylisée, éléments de communication intégrés, et références visuelles aux assurances suisses et au secteur médical.
            </div>
        </div>
        
        <div class="logo-section">
            <h2>Version 2 - Logo Moderne Épuré</h2>
            <div class="logo-container">
                <object data="capsulcall-logo-modern.svg" type="image/svg+xml" width="500" height="150">
                    Votre navigateur ne supporte pas les SVG
                </object>
            </div>
            <div class="description">
                Design moderne et minimaliste avec typographie contemporaine, parfait pour les applications numériques et l'impression.
            </div>
        </div>
        
        <div class="features">
            <h3>Caractéristiques des Logos</h3>
            <ul>
                <li><strong>Couleur principale :</strong> Bleu moderne (#1e40af à #3b82f6)</li>
                <li><strong>Style :</strong> Moderne, professionnel et accessible</li>
                <li><strong>Éléments visuels :</strong> Capsule, ondes de communication, références médicales et suisses</li>
                <li><strong>Format :</strong> SVG vectoriel (redimensionnable sans perte de qualité)</li>
                <li><strong>Utilisation :</strong> Web, impression, signalétique, cartes de visite</li>
                <li><strong>Secteurs représentés :</strong> Centre d'appel, assurances suisses, télé-secrétariat médical</li>
            </ul>
        </div>
        
        <div class="download-section">
            <h3>Fichiers Disponibles</h3>
            <div class="file-list">
                <div class="file-item">
                    <strong>capsulcall-logo.svg</strong><br>
                    Version classique
                </div>
                <div class="file-item">
                    <strong>capsulcall-logo-modern.svg</strong><br>
                    Version moderne
                </div>
                <div class="file-item">
                    <strong>preview.html</strong><br>
                    Aperçu des logos
                </div>
            </div>
        </div>
    </div>
</body>
</html>
