<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Télécharger CapsulCall Logo SVG</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            text-align: center;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(30, 64, 175, 0.1);
        }
        
        h1 {
            color: #1e40af;
            margin-bottom: 30px;
        }
        
        .logo-preview {
            margin: 30px 0;
            padding: 30px;
            background: #f8fafc;
            border-radius: 15px;
            border: 2px dashed #cbd5e1;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            margin: 15px;
            transition: transform 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        
        .file-info {
            background: #eff6ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
        
        .file-info h3 {
            color: #1e40af;
            margin-top: 0;
        }
        
        .advantages {
            background: #f0fdf4;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
        
        .advantages h3 {
            color: #166534;
            margin-top: 0;
        }
        
        .advantages ul {
            color: #374151;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 Télécharger CapsulCall Logo SVG</h1>
        
        <div class="logo-preview">
            <object data="capsulcall-logo-modern.svg" type="image/svg+xml" width="500" height="150">
                Aperçu du logo CapsulCall
            </object>
        </div>
        
        <a href="capsulcall-logo-modern.svg" download="capsulcall-logo-modern.svg" class="download-btn">
            📥 Télécharger capsulcall-logo-modern.svg
        </a>
        
        <div class="file-info">
            <h3>📋 Informations du fichier :</h3>
            <ul>
                <li><strong>Nom :</strong> capsulcall-logo-modern.svg</li>
                <li><strong>Format :</strong> SVG (Scalable Vector Graphics)</li>
                <li><strong>Dimensions :</strong> 500x150 pixels (redimensionnable)</li>
                <li><strong>Couleurs :</strong> Bleu moderne (#1e40af à #3b82f6)</li>
                <li><strong>Taille :</strong> Très léger (quelques Ko)</li>
            </ul>
        </div>
        
        <div class="advantages">
            <h3>✅ Avantages du format SVG :</h3>
            <ul>
                <li><strong>Vectoriel :</strong> Redimensionnable à l'infini sans perte de qualité</li>
                <li><strong>Léger :</strong> Taille de fichier très petite</li>
                <li><strong>Modifiable :</strong> Peut être édité avec du code ou des logiciels de design</li>
                <li><strong>Web-friendly :</strong> Parfait pour les sites web et applications</li>
                <li><strong>Impression :</strong> Qualité parfaite à toutes les tailles</li>
                <li><strong>Responsive :</strong> S'adapte automatiquement aux écrans</li>
            </ul>
        </div>
        
        <div class="file-info">
            <h3>🎯 Utilisations recommandées :</h3>
            <ul>
                <li>Sites web et applications</li>
                <li>Cartes de visite et papeterie</li>
                <li>Signalétique et panneaux</li>
                <li>Présentations PowerPoint</li>
                <li>Réseaux sociaux (profils, couvertures)</li>
                <li>Documents officiels</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Vérifier si le fichier SVG existe et afficher un message si nécessaire
        window.onload = function() {
            const object = document.querySelector('object');
            object.addEventListener('load', function() {
                console.log('Logo SVG chargé avec succès');
            });
            
            object.addEventListener('error', function() {
                console.log('Erreur de chargement du SVG');
                object.innerHTML = '<div style="padding: 50px; color: #64748b;">Aperçu du logo non disponible</div>';
            });
        };
    </script>
</body>
</html>
