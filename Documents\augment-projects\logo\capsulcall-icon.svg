<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient bleu pour l'icône -->
    <linearGradient id="iconBlue" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient accent -->
    <linearGradient id="iconAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Fond rond -->
  <circle cx="32" cy="32" r="30" fill="url(#iconBlue)"/>
  
  <!-- Capsule centrale -->
  <rect x="20" y="16" width="24" height="32" rx="12" fill="white" opacity="0.9"/>
  
  <!-- Ligne de séparation -->
  <rect x="22" y="30" width="20" height="1.5" fill="url(#iconAccent)"/>
  
  <!-- Point de communication -->
  <circle cx="32" cy="24" r="3" fill="url(#iconAccent)"/>
  <circle cx="32" cy="24" r="1.5" fill="white"/>
  
  <!-- Ondes de signal simplifiées -->
  <g transform="translate(45, 28)" opacity="0.8">
    <path d="M 0 4 Q 2 2 4 4 Q 2 6 0 4" fill="none" stroke="white" stroke-width="1.5"/>
    <path d="M 5 4 Q 7 2 9 4 Q 7 6 5 4" fill="none" stroke="white" stroke-width="1.5" opacity="0.7"/>
  </g>
  
  <!-- Lettre C stylisée en bas -->
  <text x="32" y="44" font-family="Arial, sans-serif" font-size="8" font-weight="bold" 
        fill="url(#iconAccent)" text-anchor="middle">C</text>
</svg>
