<?xml version="1.0" encoding="UTF-8"?>
<svg width="500" height="150" viewBox="0 0 500 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient bleu moderne principal -->
    <linearGradient id="primaryBlue" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient bleu accent -->
    <linearGradient id="accentBlue" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    
    <!-- Ombre moderne -->
    <filter id="modernShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e40af" flood-opacity="0.15"/>
    </filter>
  </defs>
  
  <!-- Fond -->
  <rect x="0" y="0" width="500" height="150" fill="white"/>
  
  <!-- Logo icon - Capsule moderne avec éléments de communication -->
  <g transform="translate(30, 25)">
    <!-- Capsule principale avec design moderne -->
    <rect x="0" y="20" width="60" height="80" rx="30" fill="url(#primaryBlue)" filter="url(#modernShadow)"/>
    
    <!-- Élément de séparation dans la capsule -->
    <rect x="5" y="55" width="50" height="2" fill="white" opacity="0.8"/>
    
    <!-- Indicateur de communication (point lumineux) -->
    <circle cx="30" cy="40" r="6" fill="url(#accentBlue)"/>
    <circle cx="30" cy="40" r="3" fill="white" opacity="0.9"/>
    
    <!-- Ondes de signal -->
    <g transform="translate(65, 50)" opacity="0.7">
      <path d="M 0 10 Q 5 5 10 10 Q 5 15 0 10" fill="none" stroke="url(#accentBlue)" stroke-width="2"/>
      <path d="M 12 10 Q 17 5 22 10 Q 17 15 12 10" fill="none" stroke="url(#accentBlue)" stroke-width="2" opacity="0.7"/>
      <path d="M 24 10 Q 29 5 34 10 Q 29 15 24 10" fill="none" stroke="url(#accentBlue)" stroke-width="2" opacity="0.5"/>
    </g>
  </g>
  
  <!-- Texte principal CapsulCall -->
  <g transform="translate(150, 45)">
    <!-- CapsulCall avec typographie moderne -->
    <text x="0" y="35" font-family="'Segoe UI', Arial, sans-serif" font-size="36" font-weight="700" fill="url(#primaryBlue)">
      CapsulCall
    </text>
    
    <!-- Point d'accent sur le 'i' stylisé -->
    <circle cx="285" cy="15" r="3" fill="url(#accentBlue)"/>
  </g>
  
  <!-- Ligne de séparation élégante -->
  <rect x="150" y="85" width="280" height="1" fill="url(#accentBlue)" opacity="0.3"/>
  
  <!-- Sous-titre avec icônes -->
  <g transform="translate(150, 100)">
    <!-- Icône assurance -->
    <g transform="translate(0, 0)">
      <rect x="0" y="5" width="12" height="8" rx="2" fill="none" stroke="url(#primaryBlue)" stroke-width="1.5"/>
      <path d="M 3 5 L 6 2 L 9 5" fill="none" stroke="url(#primaryBlue)" stroke-width="1.5"/>
    </g>
    <text x="20" y="12" font-family="'Segoe UI', Arial, sans-serif" font-size="11" fill="#64748b" font-weight="500">
      Assurances Suisses
    </text>
    
    <!-- Séparateur -->
    <circle cx="140" cy="8" r="1.5" fill="#cbd5e1"/>
    
    <!-- Icône médical -->
    <g transform="translate(155, 0)">
      <circle cx="6" cy="8" r="5" fill="none" stroke="url(#primaryBlue)" stroke-width="1.5"/>
      <path d="M 4 8 L 8 8 M 6 6 L 6 10" stroke="url(#primaryBlue)" stroke-width="1.5"/>
    </g>
    <text x="175" y="12" font-family="'Segoe UI', Arial, sans-serif" font-size="11" fill="#64748b" font-weight="500">
      Télé-Secrétariat Médical
    </text>
  </g>
  
  <!-- Éléments décoratifs géométriques -->
  <g transform="translate(420, 30)" opacity="0.1">
    <rect x="0" y="0" width="20" height="20" fill="url(#primaryBlue)" transform="rotate(45 10 10)"/>
    <rect x="25" y="25" width="15" height="15" fill="url(#accentBlue)" transform="rotate(45 32.5 32.5)"/>
  </g>
</svg>
