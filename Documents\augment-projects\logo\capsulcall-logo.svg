<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient bleu moderne -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient bleu clair pour les accents -->
    <linearGradient id="lightBlueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <!-- Ombre portée -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#1e40af" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Fond avec forme géométrique moderne -->
  <rect x="0" y="0" width="400" height="200" fill="#f8fafc" rx="20"/>
  
  <!-- Élément décoratif - Capsule stylisée -->
  <g transform="translate(30, 50)">
    <!-- Capsule principale -->
    <ellipse cx="50" cy="50" rx="35" ry="50" fill="url(#blueGradient)" filter="url(#shadow)"/>
    
    <!-- Détails de la capsule -->
    <ellipse cx="50" cy="35" rx="25" ry="15" fill="url(#lightBlueGradient)" opacity="0.8"/>
    <circle cx="40" cy="30" r="3" fill="white" opacity="0.9"/>
    <circle cx="55" cy="35" r="2" fill="white" opacity="0.7"/>
  </g>
  
  <!-- Icône téléphone/casque intégrée -->
  <g transform="translate(45, 65)">
    <!-- Casque -->
    <path d="M 15 20 Q 15 10 25 10 Q 35 10 35 20 L 35 25 Q 35 30 30 30 L 20 30 Q 15 30 15 25 Z" 
          fill="white" opacity="0.9"/>
    <!-- Micro -->
    <rect x="32" y="22" width="8" height="3" rx="1.5" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Texte CapsulCall -->
  <g transform="translate(140, 70)">
    <!-- Capsul en bleu foncé -->
    <text x="0" y="30" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="url(#blueGradient)">
      Capsul
    </text>
    <!-- Call en bleu plus clair -->
    <text x="110" y="30" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="url(#lightBlueGradient)">
      Call
    </text>
  </g>
  
  <!-- Sous-titre descriptif -->
  <text x="140" y="95" font-family="Arial, sans-serif" font-size="12" fill="#64748b" font-weight="500">
    Centre d'Appel • Assurances Suisses • Télé-Secrétariat Médical
  </text>
  
  <!-- Éléments décoratifs - Ondes de communication -->
  <g transform="translate(320, 40)" opacity="0.6">
    <circle cx="20" cy="20" r="8" fill="none" stroke="url(#lightBlueGradient)" stroke-width="2"/>
    <circle cx="20" cy="20" r="15" fill="none" stroke="url(#lightBlueGradient)" stroke-width="1.5" opacity="0.7"/>
    <circle cx="20" cy="20" r="22" fill="none" stroke="url(#lightBlueGradient)" stroke-width="1" opacity="0.5"/>
  </g>
  
  <!-- Croix suisse stylisée (référence aux assurances suisses) -->
  <g transform="translate(320, 120)" opacity="0.4">
    <rect x="8" y="5" width="4" height="15" fill="url(#blueGradient)" rx="1"/>
    <rect x="3" y="10" width="14" height="4" fill="url(#blueGradient)" rx="1"/>
  </g>
  
  <!-- Icône médicale stylisée -->
  <g transform="translate(350, 115)" opacity="0.5">
    <circle cx="10" cy="10" r="8" fill="none" stroke="url(#lightBlueGradient)" stroke-width="2"/>
    <path d="M 7 10 L 13 10 M 10 7 L 10 13" stroke="white" stroke-width="2" stroke-linecap="round"/>
  </g>
</svg>
